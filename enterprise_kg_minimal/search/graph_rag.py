"""
GraphRAG Implementation for Enterprise KG

This module provides graph-based retrieval augmented generation (GraphRAG) capabilities
using the Neo4j knowledge graph. It leverages entity and relationship properties from
the constants module for intelligent graph traversal and context extraction.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime

from ..storage.neo4j_client import Neo4jClient
from ..constants.entities import (
    get_all_entity_types, 
    get_entity_properties,
    get_entity_category_mapping
)
from ..constants.relationships import (
    get_all_relationship_types,
    get_relationship_description,
    get_relationship_category_mapping,
    RelationshipType
)
from .search_schemas import (
    SearchQuery, 
    EntityMatch, 
    RelationshipMatch, 
    GraphContext,
    SearchMetrics
)

logger = logging.getLogger(__name__)


class GraphRAG:
    """
    Graph-based Retrieval Augmented Generation for Enterprise KG.
    
    This class provides intelligent graph traversal and context extraction
    using the knowledge graph structure and entity/relationship properties
    defined in the constants module.
    """
    
    def __init__(self, neo4j_client: Neo4jClient):
        """
        Initialize GraphRAG with Neo4j client.
        
        Args:
            neo4j_client: Neo4j client for graph operations
        """
        self.neo4j_client = neo4j_client
        self.entity_types = get_all_entity_types()
        self.relationship_types = get_all_relationship_types()
        self.entity_categories = get_entity_category_mapping()
        self.relationship_categories = get_relationship_category_mapping()
        
        logger.info(f"GraphRAG initialized with {len(self.entity_types)} entity types "
                   f"and {len(self.relationship_types)} relationship types")
    
    def extract_graph_context(
        self, 
        chunk_indices: List[str], 
        query: SearchQuery
    ) -> Tuple[GraphContext, SearchMetrics]:
        """
        Extract rich graph context from chunk indices.
        
        Args:
            chunk_indices: List of chunk IDs to start graph traversal from
            query: Search query with parameters and filters
            
        Returns:
            Tuple of (GraphContext, SearchMetrics)
        """
        start_time = datetime.now()
        metrics = SearchMetrics()
        
        try:
            # Step 1: Get chunks and their extracted entities
            chunks_data = self._get_chunks_data(chunk_indices)
            metrics.chunks_processed = len(chunks_data)
            
            # Step 2: Extract entities from chunks
            initial_entities = self._extract_entities_from_chunks(chunks_data, query)
            
            # Step 3: Expand graph context through traversal (hybrid or traditional)
            if query.use_hybrid_traversal:
                expanded_context = self._expand_graph_context(initial_entities, query, metrics)
            else:
                expanded_context = self._expand_graph_context_traditional(initial_entities, query, metrics)
            
            # Step 4: Extract relationships
            relationships = self._extract_relationships(expanded_context['entities'], query)
            
            # Step 5: Build final graph context
            graph_context = GraphContext(
                entities=expanded_context['entities'],
                relationships=relationships,
                source_chunks=chunks_data,
                chunk_count=len(chunks_data),
                total_entities=len(expanded_context['entities']),
                total_relationships=len(relationships),
                entity_types_found=set(e.entity_type for e in expanded_context['entities']),
                relationship_types_found=set(r.relationship_type for r in relationships),
                max_depth_reached=expanded_context['max_depth'],
                expansion_paths=expanded_context['paths']
            )
            
            # Update metrics
            end_time = datetime.now()
            metrics.total_time_ms = (end_time - start_time).total_seconds() * 1000
            metrics.entities_found = len(expanded_context['entities'])
            metrics.relationships_found = len(relationships)
            metrics.expansion_iterations = expanded_context['iterations']
            
            if expanded_context['entities']:
                metrics.avg_entity_confidence = sum(e.relevance_score for e in expanded_context['entities']) / len(expanded_context['entities'])
            if relationships:
                metrics.avg_relationship_confidence = sum(r.confidence_score for r in relationships) / len(relationships)
            
            # Calculate coverage metrics
            metrics.entity_type_coverage = len(graph_context.entity_types_found) / len(self.entity_types) if self.entity_types else 0
            metrics.relationship_type_coverage = len(graph_context.relationship_types_found) / len(self.relationship_types) if self.relationship_types else 0
            
            logger.info(f"GraphRAG extracted context: {metrics.entities_found} entities, "
                       f"{metrics.relationships_found} relationships in {metrics.total_time_ms:.2f}ms")
            
            return graph_context, metrics
            
        except Exception as e:
            logger.error(f"Error in GraphRAG context extraction: {e}")
            # Return empty context on error
            empty_context = GraphContext()
            metrics.total_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            return empty_context, metrics
    
    def _get_chunks_data(self, chunk_indices: List[str]) -> List[Dict[str, Any]]:
        """
        Retrieve chunk data from Neo4j.
        
        Args:
            chunk_indices: List of chunk IDs
            
        Returns:
            List of chunk data dictionaries
        """
        if not chunk_indices:
            return []
        
        try:
            # Query to get chunk data
            query = """
            MATCH (c:Chunk)
            WHERE c.id IN $chunk_ids
            RETURN c.id as chunk_id, c.text as text, c.chunk_index as chunk_index,
                   c.word_count as word_count, c.sentence_count as sentence_count,
                   c.created_at as created_at
            """
            
            result = self.neo4j_client.execute_query(query, {"chunk_ids": chunk_indices})
            
            chunks_data = []
            for record in result:
                chunks_data.append({
                    "chunk_id": record["chunk_id"],
                    "text": record["text"],
                    "chunk_index": record["chunk_index"],
                    "word_count": record["word_count"],
                    "sentence_count": record["sentence_count"],
                    "created_at": record["created_at"]
                })
            
            logger.debug(f"Retrieved {len(chunks_data)} chunks from {len(chunk_indices)} indices")
            return chunks_data
            
        except Exception as e:
            logger.error(f"Error retrieving chunks data: {e}")
            return []
    
    def _extract_entities_from_chunks(
        self, 
        chunks_data: List[Dict[str, Any]], 
        query: SearchQuery
    ) -> List[EntityMatch]:
        """
        Extract entities that were extracted from the given chunks.
        
        Args:
            chunks_data: List of chunk data
            query: Search query with filters
            
        Returns:
            List of EntityMatch objects
        """
        if not chunks_data:
            return []
        
        chunk_ids = [chunk["chunk_id"] for chunk in chunks_data]
        
        try:
            # Build entity type filter
            entity_type_filter = ""
            params = {"chunk_ids": chunk_ids}
            
            if query.entity_types:
                entity_type_filter = "AND e.entity_type IN $entity_types"
                params["entity_types"] = list(query.entity_types)
            
            # Query to get entities extracted from chunks
            cypher_query = f"""
            MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e)
            WHERE c.id IN $chunk_ids {entity_type_filter}
            RETURN DISTINCT e.name as name, e.entity_type as entity_type,
                   id(e) as node_id, e as entity_node,
                   collect(DISTINCT c.id) as chunk_sources,
                   e.created_at as created_at, e.updated_at as updated_at
            """
            
            result = self.neo4j_client.execute_query(cypher_query, params)
            
            entities = []
            for record in result:
                # Get enhanced properties from constants
                entity_properties = get_entity_properties(record["entity_type"])

                # Calculate base relevance score
                base_relevance = entity_properties.get("graph_importance", 0.5)

                # Add query relevance if query text is available
                query_relevance_boost = 0.0
                if hasattr(query, 'query_text') and query.query_text:
                    # Create temporary entity to calculate query relevance
                    temp_entity = EntityMatch(
                        name=record["name"],
                        entity_type=record["entity_type"],
                        node_id=str(record["node_id"]),
                        properties=entity_properties,
                        created_at=record["created_at"],
                        updated_at=record["updated_at"],
                        relevance_score=0.0,
                        match_reason="temp"
                    )
                    query_relevance = self._calculate_query_entity_relevance(temp_entity, query.query_text)
                    query_relevance_boost = query_relevance * 0.3  # 30% boost for query relevance

                # Combine base relevance with query relevance
                final_relevance = min(1.0, base_relevance + query_relevance_boost)

                entity = EntityMatch(
                    name=record["name"],
                    entity_type=record["entity_type"],
                    node_id=str(record["node_id"]),
                    properties=entity_properties,
                    chunk_sources=record["chunk_sources"],
                    created_at=record["created_at"],
                    updated_at=record["updated_at"],
                    relevance_score=final_relevance,
                    match_reason=f"extracted_from_chunk_query_rel_{query_relevance_boost:.3f}"
                )
                entities.append(entity)
            
            logger.debug(f"Extracted {len(entities)} entities from chunks")
            return entities
            
        except Exception as e:
            logger.error(f"Error extracting entities from chunks: {e}")
            return []

    def _expand_graph_context(
        self,
        initial_entities: List[EntityMatch],
        query: SearchQuery,
        metrics: SearchMetrics
    ) -> Dict[str, Any]:
        """
        Expand graph context using optimized hybrid retrieval approach.

        This method combines vector similarity scores with graph connectivity
        to optimize traversal paths and reduce latency.

        Args:
            initial_entities: Starting entities for expansion
            query: Search query with expansion parameters
            metrics: Metrics object to update

        Returns:
            Dictionary with expanded entities, max depth, paths, and iterations
        """
        if not initial_entities:
            return {
                "entities": [],
                "max_depth": 0,
                "paths": [],
                "iterations": 0
            }

        all_entities = {e.node_id: e for e in initial_entities}
        expansion_paths = []
        max_depth = 0
        iterations = 0

        # Initialize priority queue for hybrid traversal
        from heapq import heappush, heappop

        # Priority queue: (-priority_score, depth, entity_name, entity_data)
        traversal_queue = []
        visited_entities = set()

        # Add initial entities to queue with high priority
        for entity in initial_entities:
            priority_score = entity.relevance_score * 1.0  # Base priority
            heappush(traversal_queue, (-priority_score, 0, entity.name, entity))
            visited_entities.add(entity.name)

        try:
            # Hybrid traversal using priority queue
            while traversal_queue and max_depth < query.expansion_depth:
                current_priority, current_depth, entity_name, _ = heappop(traversal_queue)
                current_priority = -current_priority  # Convert back to positive

                # Skip if we've exceeded depth limit
                if current_depth >= query.expansion_depth:
                    continue

                iterations += 1
                next_depth = current_depth + 1
                max_depth = max(max_depth, next_depth)

                # Find neighboring entities with hybrid scoring
                neighbors = self._find_neighboring_entities_hybrid(
                    [entity_name],
                    query,
                    next_depth,
                    current_priority
                )

                if not neighbors:
                    continue

                # Process neighbors and add to queue
                new_entities_count = 0
                for neighbor in neighbors:
                    if neighbor.name not in visited_entities:
                        visited_entities.add(neighbor.name)

                        # Add to entities collection if not already present
                        if neighbor.node_id not in all_entities:
                            all_entities[neighbor.node_id] = neighbor
                            new_entities_count += 1

                            # Track expansion path
                            expansion_paths.append([
                                f"depth_{next_depth}",
                                neighbor.name,
                                neighbor.entity_type,
                                neighbor.match_reason,
                                f"priority_{neighbor.relevance_score:.3f}"
                            ])

                        # Add to traversal queue if within depth limit
                        if next_depth < query.expansion_depth:
                            heappush(traversal_queue,
                                   (-neighbor.relevance_score, next_depth, neighbor.name, neighbor))

                logger.debug(f"Depth {next_depth}: Found {new_entities_count} new entities "
                           f"(priority: {current_priority:.3f})")

                # Early termination if queue becomes too large (performance optimization)
                if len(traversal_queue) > query.priority_queue_limit:
                    logger.warning(f"Traversal queue size exceeded limit ({query.priority_queue_limit}), terminating early")
                    break

            final_entities = list(all_entities.values())

            # Update metrics
            metrics.expansion_iterations = iterations

            logger.debug(f"Hybrid graph expansion completed: {len(final_entities)} total entities, "
                        f"max depth {max_depth}, {iterations} iterations")

            return {
                "entities": final_entities,
                "max_depth": max_depth,
                "paths": expansion_paths,
                "iterations": iterations
            }

        except Exception as e:
            logger.error(f"Error in hybrid graph expansion: {e}")
            return {
                "entities": list(all_entities.values()),
                "max_depth": max_depth,
                "paths": expansion_paths,
                "iterations": iterations
            }

    def _find_neighboring_entities(
        self,
        entity_names: List[str],
        query: SearchQuery,
        depth: int
    ) -> List[EntityMatch]:
        """
        Find entities connected to the given entities through relationships.

        Args:
            entity_names: Names of entities to find neighbors for
            query: Search query with filters
            depth: Current expansion depth

        Returns:
            List of neighboring EntityMatch objects
        """
        if not entity_names:
            return []

        try:
            # Build filters
            entity_type_filter = ""
            relationship_type_filter = ""
            params = {
                "entity_names": entity_names,
                "min_confidence": query.min_confidence_score
            }

            if query.entity_types:
                entity_type_filter = "AND neighbor.entity_type IN $entity_types"
                params["entity_types"] = list(query.entity_types)

            if query.relationship_types:
                relationship_type_filter = "AND type(r) IN $relationship_types"
                params["relationship_types"] = list(query.relationship_types)

            # Query for neighboring entities
            cypher_query = f"""
            MATCH (entity)-[r]-(neighbor)
            WHERE entity.name IN $entity_names
            AND (r.confidence_score IS NULL OR r.confidence_score >= $min_confidence)
            {relationship_type_filter}
            {entity_type_filter}
            RETURN DISTINCT neighbor.name as name, neighbor.entity_type as entity_type,
                   id(neighbor) as node_id, neighbor as entity_node,
                   type(r) as relationship_type, r.confidence_score as confidence,
                   neighbor.created_at as created_at, neighbor.updated_at as updated_at
            LIMIT 100
            """

            result = self.neo4j_client.execute_query(cypher_query, params)

            neighbors = []
            for record in result:
                # Get enhanced properties from constants
                entity_properties = get_entity_properties(record["entity_type"])

                # Calculate relevance score based on depth and properties
                base_score = entity_properties.get("graph_importance", 0.5)
                depth_penalty = 0.1 * depth  # Reduce score for deeper entities
                confidence_boost = (record["confidence"] or 0.5) * 0.2
                relevance_score = max(0.1, base_score - depth_penalty + confidence_boost)

                neighbor = EntityMatch(
                    name=record["name"],
                    entity_type=record["entity_type"],
                    node_id=str(record["node_id"]),
                    properties=entity_properties,
                    created_at=record["created_at"],
                    updated_at=record["updated_at"],
                    relevance_score=relevance_score,
                    match_reason=f"neighbor_depth_{depth}_via_{record['relationship_type']}"
                )
                neighbors.append(neighbor)

            logger.debug(f"Found {len(neighbors)} neighbors at depth {depth}")
            return neighbors

        except Exception as e:
            logger.error(f"Error finding neighboring entities: {e}")
            return []

    def _find_neighboring_entities_hybrid(
        self,
        entity_names: List[str],
        query: SearchQuery,
        depth: int,
        parent_priority: float
    ) -> List[EntityMatch]:
        """
        Find neighboring entities using hybrid scoring that combines graph connectivity
        with vector similarity and relationship strength.

        Args:
            entity_names: Names of entities to find neighbors for
            query: Search query with filters
            depth: Current expansion depth
            parent_priority: Priority score of parent entity

        Returns:
            List of neighboring EntityMatch objects with hybrid relevance scores
        """
        if not entity_names:
            return []

        try:
            # Build filters
            entity_type_filter = ""
            relationship_type_filter = ""
            params = {
                "entity_names": entity_names,
                "min_confidence": query.min_confidence_score
            }

            if query.entity_types:
                entity_type_filter = "AND neighbor.entity_type IN $entity_types"
                params["entity_types"] = list(query.entity_types)

            if query.relationship_types:
                relationship_type_filter = "AND type(r) IN $relationship_types"
                params["relationship_types"] = list(query.relationship_types)

            # Enhanced query that includes relationship strength and entity connectivity
            cypher_query = f"""
            MATCH (entity)-[r]-(neighbor)
            WHERE entity.name IN $entity_names
            AND (r.confidence_score IS NULL OR r.confidence_score >= $min_confidence)
            {relationship_type_filter}
            {entity_type_filter}

            // Calculate connectivity metrics
            OPTIONAL MATCH (neighbor)-[neighbor_rels]-()
            WITH neighbor, r, entity, count(neighbor_rels) as connectivity_count

            // Calculate chunk co-occurrence (entities appearing in same chunks)
            OPTIONAL MATCH (entity)<-[:EXTRACTED_FROM]-(chunk:Chunk)-[:EXTRACTED_FROM]->(neighbor)
            WITH neighbor, r, entity, connectivity_count, count(DISTINCT chunk) as chunk_cooccurrence

            RETURN DISTINCT
                neighbor.name as name,
                neighbor.entity_type as entity_type,
                id(neighbor) as node_id,
                neighbor as entity_node,
                type(r) as relationship_type,
                r.confidence_score as confidence,
                neighbor.created_at as created_at,
                neighbor.updated_at as updated_at,
                connectivity_count,
                chunk_cooccurrence
            ORDER BY
                r.confidence_score DESC,
                connectivity_count DESC,
                chunk_cooccurrence DESC
            LIMIT 50
            """

            result = self.neo4j_client.execute_query(cypher_query, params)

            neighbors = []
            for record in result:
                # Get enhanced properties from constants
                entity_properties = get_entity_properties(record["entity_type"])

                # Hybrid relevance scoring using configurable weights
                base_score = entity_properties.get("graph_importance", 0.5)

                # Relationship confidence component (0.0-1.0)
                confidence_score = record["confidence"] or 0.5
                confidence_component = confidence_score * query.confidence_weight

                # Graph connectivity component (normalized)
                connectivity_count = record["connectivity_count"] or 0
                connectivity_component = min(connectivity_count / 10.0, 1.0) * query.connectivity_weight

                # Chunk co-occurrence component (entities in same chunks are more relevant)
                chunk_cooccurrence = record["chunk_cooccurrence"] or 0
                cooccurrence_component = min(chunk_cooccurrence / 5.0, 1.0) * query.cooccurrence_weight

                # Parent priority inheritance (entities connected to high-priority entities get boost)
                priority_inheritance = parent_priority * query.priority_inheritance_weight

                # Depth penalty (reduce score for deeper entities)
                depth_penalty = 0.05 * depth

                # NEW: Calculate query relevance component
                query_relevance = 0.0
                if hasattr(query, 'query_text') and query.query_text:
                    # Create temporary entity to calculate query relevance
                    temp_entity = EntityMatch(
                        name=record["name"],
                        entity_type=record["entity_type"],
                        node_id=str(record["node_id"]),
                        properties=entity_properties,
                        created_at=record["created_at"],
                        updated_at=record["updated_at"],
                        relevance_score=0.0,  # Will be calculated
                        match_reason="temp"
                    )
                    query_relevance = self._calculate_query_entity_relevance(temp_entity, query.query_text)

                query_component = query_relevance * 0.15  # 15% weight for query relevance

                # Calculate final hybrid score with query awareness
                relevance_score = max(0.1,
                    base_score * 0.85 +           # Reduced from 1.0 to accommodate query component
                    confidence_component * 0.85 +  # Reduced weight
                    connectivity_component * 0.85 + # Reduced weight
                    cooccurrence_component * 0.85 +  # Reduced weight
                    query_component +              # NEW: Query relevance component
                    priority_inheritance -
                    depth_penalty
                )

                neighbor = EntityMatch(
                    name=record["name"],
                    entity_type=record["entity_type"],
                    node_id=str(record["node_id"]),
                    properties=entity_properties,
                    created_at=record["created_at"],
                    updated_at=record["updated_at"],
                    relevance_score=relevance_score,
                    match_reason=f"hybrid_neighbor_depth_{depth}_via_{record['relationship_type']}_conn_{connectivity_count}_cooc_{chunk_cooccurrence}"
                )
                neighbors.append(neighbor)

            # Sort by relevance score (highest first) for priority queue
            neighbors.sort(key=lambda x: x.relevance_score, reverse=True)

            logger.debug(f"Found {len(neighbors)} hybrid neighbors at depth {depth} "
                        f"(parent priority: {parent_priority:.3f})")
            return neighbors

        except Exception as e:
            logger.error(f"Error finding hybrid neighboring entities: {e}")
            # Fallback to regular neighbor finding
            return self._find_neighboring_entities(entity_names, query, depth)

    def _expand_graph_context_traditional(
        self,
        initial_entities: List[EntityMatch],
        query: SearchQuery,
        metrics: SearchMetrics
    ) -> Dict[str, Any]:
        """
        Traditional breadth-first graph expansion (original implementation).

        This method provides the original BFS traversal for backward compatibility
        and comparison with the hybrid approach.

        Args:
            initial_entities: Starting entities for expansion
            query: Search query with expansion parameters
            metrics: Metrics object to update

        Returns:
            Dictionary with expanded entities, max depth, paths, and iterations
        """
        if not initial_entities:
            return {
                "entities": [],
                "max_depth": 0,
                "paths": [],
                "iterations": 0
            }

        all_entities = {e.node_id: e for e in initial_entities}
        expansion_paths = []
        max_depth = 0
        iterations = 0

        # Get entity names for traversal
        current_entities = [e.name for e in initial_entities]

        try:
            for depth in range(1, query.expansion_depth + 1):
                iterations += 1
                max_depth = depth

                # Find neighboring entities using traditional method
                neighbors = self._find_neighboring_entities(
                    current_entities,
                    query,
                    depth
                )

                if not neighbors:
                    break

                # Add new entities to collection
                new_entities = []
                for neighbor in neighbors:
                    if neighbor.node_id not in all_entities:
                        all_entities[neighbor.node_id] = neighbor
                        new_entities.append(neighbor)

                        # Track expansion path
                        expansion_paths.append([
                            f"depth_{depth}",
                            neighbor.name,
                            neighbor.entity_type,
                            neighbor.match_reason
                        ])

                # Prepare for next iteration
                current_entities = [e.name for e in new_entities]

                # Stop if no new entities found
                if not new_entities:
                    break

                logger.debug(f"Traditional depth {depth}: Found {len(new_entities)} new entities")

            final_entities = list(all_entities.values())

            # Update metrics
            metrics.expansion_iterations = iterations

            logger.debug(f"Traditional graph expansion completed: {len(final_entities)} total entities, "
                        f"max depth {max_depth}, {iterations} iterations")

            return {
                "entities": final_entities,
                "max_depth": max_depth,
                "paths": expansion_paths,
                "iterations": iterations
            }

        except Exception as e:
            logger.error(f"Error in traditional graph expansion: {e}")
            return {
                "entities": list(all_entities.values()),
                "max_depth": max_depth,
                "paths": expansion_paths,
                "iterations": iterations
            }

    def _extract_relationships(
        self,
        entities: List[EntityMatch],
        query: SearchQuery
    ) -> List[RelationshipMatch]:
        """
        Extract relationships between the given entities.

        Args:
            entities: List of entities to find relationships between
            query: Search query with filters

        Returns:
            List of RelationshipMatch objects
        """
        if len(entities) < 2:
            return []

        entity_names = [e.name for e in entities]

        try:
            # Build relationship type filter
            relationship_type_filter = ""
            params = {
                "entity_names": entity_names,
                "min_confidence": query.min_confidence_score
            }

            if query.relationship_types:
                relationship_type_filter = "AND type(r) IN $relationship_types"
                params["relationship_types"] = list(query.relationship_types)

            # Query for relationships between entities
            cypher_query = f"""
            MATCH (source)-[r]->(target)
            WHERE source.name IN $entity_names
            AND target.name IN $entity_names
            AND source <> target
            AND (r.confidence_score IS NULL OR r.confidence_score >= $min_confidence)
            {relationship_type_filter}
            RETURN source.name as source_entity, target.name as target_entity,
                   type(r) as relationship_type, r as relationship,
                   r.confidence_score as confidence_score, r.context as context,
                   r.source_sentence as source_sentence, r.created_at as created_at
            """

            result = self.neo4j_client.execute_query(cypher_query, params)

            relationships = []
            for record in result:
                # Get relationship description from constants
                rel_type = record["relationship_type"]
                try:
                    # Try to get enum value and description
                    rel_enum = RelationshipType(rel_type)
                    rel_description = get_relationship_description(rel_enum)
                except (ValueError, TypeError):
                    # Fallback if relationship type not found in enum
                    rel_description = f"Relationship of type {rel_type}"

                # Calculate relevance score
                confidence = record["confidence_score"] or 0.5
                relevance_score = confidence * 0.8 + 0.2  # Base relevance with confidence boost

                relationship = RelationshipMatch(
                    source_entity=record["source_entity"],
                    target_entity=record["target_entity"],
                    relationship_type=rel_type,
                    properties={"description": rel_description},
                    confidence_score=confidence,
                    context=record["context"],
                    source_sentence=record["source_sentence"],
                    relevance_score=relevance_score,
                    match_reason="entity_connection",
                    created_at=record["created_at"]
                )
                relationships.append(relationship)

            logger.debug(f"Extracted {len(relationships)} relationships between entities")
            return relationships

        except Exception as e:
            logger.error(f"Error extracting relationships: {e}")
            return []

    def get_entity_neighborhood(
        self,
        entity_name: str,
        max_depth: int = 2,
        entity_types: Optional[Set[str]] = None,
        relationship_types: Optional[Set[str]] = None
    ) -> GraphContext:
        """
        Get the neighborhood context around a specific entity.

        Args:
            entity_name: Name of the central entity
            max_depth: Maximum traversal depth
            entity_types: Optional entity type filters
            relationship_types: Optional relationship type filters

        Returns:
            GraphContext with neighborhood information
        """
        try:
            # Create a search query for the neighborhood
            query = SearchQuery(
                chunk_indices=[],  # Not used for neighborhood search
                expansion_depth=max_depth,
                entity_types=entity_types,
                relationship_types=relationship_types
            )

            # Get the central entity
            central_entity = self._get_entity_by_name(entity_name)
            if not central_entity:
                return GraphContext()

            # Expand from the central entity
            metrics = SearchMetrics()
            expanded_context = self._expand_graph_context([central_entity], query, metrics)

            # Extract relationships
            relationships = self._extract_relationships(expanded_context['entities'], query)

            # Build graph context
            graph_context = GraphContext(
                entities=expanded_context['entities'],
                relationships=relationships,
                total_entities=len(expanded_context['entities']),
                total_relationships=len(relationships),
                entity_types_found=set(e.entity_type for e in expanded_context['entities']),
                relationship_types_found=set(r.relationship_type for r in relationships),
                max_depth_reached=expanded_context['max_depth'],
                expansion_paths=expanded_context['paths']
            )

            return graph_context

        except Exception as e:
            logger.error(f"Error getting entity neighborhood for {entity_name}: {e}")
            return GraphContext()

    def _get_entity_by_name(self, entity_name: str) -> Optional[EntityMatch]:
        """
        Get an entity by name.

        Args:
            entity_name: Name of the entity to find

        Returns:
            EntityMatch object if found, None otherwise
        """
        try:
            query = """
            MATCH (e {name: $entity_name})
            RETURN e.name as name, e.entity_type as entity_type,
                   id(e) as node_id, e.created_at as created_at,
                   e.updated_at as updated_at
            LIMIT 1
            """

            result = self.neo4j_client.execute_query(query, {"entity_name": entity_name})

            if result:
                record = result[0]
                entity_properties = get_entity_properties(record["entity_type"])

                return EntityMatch(
                    name=record["name"],
                    entity_type=record["entity_type"],
                    node_id=str(record["node_id"]),
                    properties=entity_properties,
                    created_at=record["created_at"],
                    updated_at=record["updated_at"],
                    relevance_score=entity_properties.get("graph_importance", 0.5),
                    match_reason="direct_lookup"
                )

            return None

        except Exception as e:
            logger.error(f"Error getting entity by name {entity_name}: {e}")
            return None

    def _calculate_query_entity_relevance(self, entity: EntityMatch, query_text: str) -> float:
        """
        Calculate semantic relevance between query text and entity.

        This method computes how relevant an entity is to the original query
        by analyzing keyword overlap, semantic similarity, and entity properties.

        Args:
            entity: Entity to calculate relevance for
            query_text: Original query text

        Returns:
            Relevance score between 0.0 and 1.0
        """
        if not query_text or not entity.name:
            return 0.0

        try:
            # Normalize and tokenize query text
            query_keywords = self._extract_keywords(query_text.lower())

            # Extract entity keywords from name and properties
            entity_keywords = self._extract_entity_keywords(entity)

            if not query_keywords or not entity_keywords:
                return 0.0

            # Calculate keyword overlap score
            overlap_score = self._calculate_keyword_overlap(query_keywords, entity_keywords)

            # Calculate semantic similarity score
            semantic_score = self._calculate_semantic_similarity(query_text, entity)

            # Calculate entity type relevance
            type_relevance = self._calculate_type_relevance(query_text, entity.entity_type)

            # Combine scores with weights
            final_score = (
                overlap_score * 0.4 +      # 40% keyword overlap
                semantic_score * 0.4 +     # 40% semantic similarity
                type_relevance * 0.2       # 20% type relevance
            )

            return min(1.0, max(0.0, final_score))

        except Exception as e:
            logger.error(f"Error calculating query-entity relevance: {e}")
            return 0.0

    def _extract_keywords(self, text: str) -> Set[str]:
        """
        Extract meaningful keywords from text.

        Args:
            text: Input text to extract keywords from

        Returns:
            Set of normalized keywords
        """
        if not text:
            return set()

        # Remove special characters and split into words
        words = re.findall(r'\b\w+\b', text.lower())

        # Filter out common stop words and short words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we',
            'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'its',
            'our', 'their', 'what', 'when', 'where', 'why', 'how', 'who', 'which'
        }

        # Keep words that are longer than 2 characters and not stop words
        keywords = {word for word in words if len(word) > 2 and word not in stop_words}

        return keywords

    def _extract_entity_keywords(self, entity: EntityMatch) -> Set[str]:
        """
        Extract keywords from entity name and properties.

        Args:
            entity: Entity to extract keywords from

        Returns:
            Set of entity keywords
        """
        keywords = set()

        # Extract from entity name
        if entity.name:
            keywords.update(self._extract_keywords(entity.name))

        # Extract from entity properties
        if entity.properties:
            for key, value in entity.properties.items():
                if isinstance(value, str):
                    keywords.update(self._extract_keywords(value))
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, str):
                            keywords.update(self._extract_keywords(item))

        # Add entity type as keyword
        if entity.entity_type:
            keywords.update(self._extract_keywords(entity.entity_type))

        return keywords

    def _calculate_keyword_overlap(self, query_keywords: Set[str], entity_keywords: Set[str]) -> float:
        """
        Calculate keyword overlap score between query and entity.

        Args:
            query_keywords: Set of query keywords
            entity_keywords: Set of entity keywords

        Returns:
            Overlap score between 0.0 and 1.0
        """
        if not query_keywords or not entity_keywords:
            return 0.0

        # Calculate Jaccard similarity
        intersection = len(query_keywords & entity_keywords)
        union = len(query_keywords | entity_keywords)

        if union == 0:
            return 0.0

        jaccard_score = intersection / union

        # Also calculate coverage (how much of query is covered)
        query_coverage = intersection / len(query_keywords)

        # Combine Jaccard and coverage with weights
        overlap_score = jaccard_score * 0.6 + query_coverage * 0.4

        return overlap_score

    def _calculate_semantic_similarity(self, query_text: str, entity: EntityMatch) -> float:
        """
        Calculate semantic similarity between query and entity.

        For now, this uses simple heuristics. Can be enhanced with embeddings later.

        Args:
            query_text: Original query text
            entity: Entity to compare against

        Returns:
            Semantic similarity score between 0.0 and 1.0
        """
        try:
            # Simple semantic similarity based on:
            # 1. Partial string matching
            # 2. Common word patterns
            # 3. Entity context relevance

            query_lower = query_text.lower()
            entity_name_lower = entity.name.lower() if entity.name else ""

            # Check for partial matches
            partial_match_score = 0.0
            if entity_name_lower in query_lower or query_lower in entity_name_lower:
                partial_match_score = 0.8
            elif any(word in entity_name_lower for word in query_lower.split() if len(word) > 3):
                partial_match_score = 0.4

            # Check for semantic patterns (e.g., "project" query matching "Project" entities)
            pattern_score = 0.0
            query_words = set(query_lower.split())
            entity_type_words = set(entity.entity_type.lower().split()) if entity.entity_type else set()

            if query_words & entity_type_words:
                pattern_score = 0.3

            # Check entity properties for context relevance
            context_score = 0.0
            if entity.properties:
                for prop_value in entity.properties.values():
                    if isinstance(prop_value, str) and any(word in prop_value.lower() for word in query_words if len(word) > 3):
                        context_score = 0.2
                        break

            # Combine scores
            semantic_score = max(partial_match_score, pattern_score + context_score)

            return min(1.0, semantic_score)

        except Exception as e:
            logger.error(f"Error calculating semantic similarity: {e}")
            return 0.0

    def _calculate_type_relevance(self, query_text: str, entity_type: str) -> float:
        """
        Calculate how relevant an entity type is to the query.

        Args:
            query_text: Original query text
            entity_type: Entity type to evaluate

        Returns:
            Type relevance score between 0.0 and 1.0
        """
        if not query_text or not entity_type:
            return 0.0

        try:
            query_lower = query_text.lower()
            type_lower = entity_type.lower()

            # Direct type mention
            if type_lower in query_lower:
                return 1.0

            # Check for related terms
            type_mappings = {
                'person': ['people', 'employee', 'staff', 'team', 'member', 'user', 'individual'],
                'company': ['organization', 'business', 'firm', 'corporation', 'enterprise'],
                'project': ['initiative', 'program', 'task', 'work', 'development'],
                'technology': ['tech', 'system', 'platform', 'tool', 'software'],
                'process': ['procedure', 'workflow', 'method', 'approach'],
                'team': ['group', 'department', 'unit', 'squad'],
                'system': ['platform', 'infrastructure', 'architecture', 'framework'],
                'tool': ['software', 'application', 'utility', 'instrument']
            }

            # Check if query mentions related terms
            for base_type, related_terms in type_mappings.items():
                if base_type in type_lower:
                    for term in related_terms:
                        if term in query_lower:
                            return 0.7

            # Check for general business/technical context
            business_terms = ['business', 'work', 'company', 'organization', 'team', 'project']
            tech_terms = ['technology', 'system', 'platform', 'software', 'development']

            if any(term in query_lower for term in business_terms):
                if entity_type in ['Person', 'Company', 'Team', 'Project', 'Process']:
                    return 0.3

            if any(term in query_lower for term in tech_terms):
                if entity_type in ['Technology', 'System', 'Platform', 'Tool']:
                    return 0.3

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating type relevance: {e}")
            return 0.0
