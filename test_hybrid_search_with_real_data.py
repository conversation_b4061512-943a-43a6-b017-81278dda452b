#!/usr/bin/env python3
"""
Test Hybrid Search Engine with Real Neo4j Data

This script tests the hybrid search engine with actual data in Neo4j,
checking traversal and scoring functionality.
"""

import os
import sys
from typing import List, Set
from dotenv import load_dotenv

# Add the enterprise_kg_minimal to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'enterprise_kg_minimal'))

load_dotenv()

def check_neo4j_data():
    """Check what data exists in Neo4j for testing."""
    print("🔍 Checking Neo4j Database Content")
    print("=" * 50)
    
    try:
        from neo4j import GraphDatabase
        
        uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        user = os.getenv('NEO4J_USER', 'neo4j')
        password = os.getenv('NEO4J_PASSWORD', 'password')
        
        print(f"Connecting to: {uri}")
        
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        with driver.session() as session:
            # Check node counts by label
            result = session.run("MATCH (n) RETURN labels(n) as labels, count(n) as count")
            print("\n📊 Node Counts by Label:")
            node_data = {}
            for record in result:
                labels = record['labels']
                count = record['count']
                label_str = ':'.join(labels) if labels else 'No Label'
                node_data[label_str] = count
                print(f"   {label_str}: {count}")
            
            # Check relationship counts
            result = session.run("MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count")
            print("\n🔗 Relationship Counts by Type:")
            rel_data = {}
            for record in result:
                rel_type = record['rel_type']
                count = record['count']
                rel_data[rel_type] = count
                print(f"   {rel_type}: {count}")
            
            # Get sample entities for testing
            print("\n🎯 Sample Entities for Testing:")
            
            # Get some Person entities
            result = session.run("MATCH (p:Person) RETURN p.name LIMIT 5")
            persons = [record['p.name'] for record in result]
            if persons:
                print(f"   Persons: {persons}")
            
            # Get some Company entities
            result = session.run("MATCH (c:Company) RETURN c.name LIMIT 5")
            companies = [record['c.name'] for record in result]
            if companies:
                print(f"   Companies: {companies}")
            
            # Get some Project entities
            result = session.run("MATCH (p:Project) RETURN p.name LIMIT 5")
            projects = [record['p.name'] for record in result]
            if projects:
                print(f"   Projects: {projects}")
            
            # Get some chunks for testing
            result = session.run("MATCH (c:Chunk) RETURN c.id LIMIT 5")
            chunks = [record['c.id'] for record in result]
            if chunks:
                print(f"   Sample Chunk IDs: {chunks}")
            
            driver.close()
            
            return {
                'nodes': node_data,
                'relationships': rel_data,
                'sample_entities': {
                    'persons': persons,
                    'companies': companies,
                    'projects': projects
                },
                'sample_chunks': chunks
            }
            
    except Exception as e:
        print(f"❌ Error checking Neo4j data: {e}")
        return None

def test_hybrid_search_initialization():
    """Test hybrid search engine initialization."""
    print("\n🚀 Testing Hybrid Search Engine Initialization")
    print("=" * 50)
    
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        
        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )
        
        # Test getting available types
        available_types = search_engine.get_available_types()
        
        print("✅ Search engine initialized successfully")
        print(f"   Entity types available: {available_types['total_entity_types']}")
        print(f"   Relationship types available: {available_types['total_relationship_types']}")
        print(f"   Entity categories: {len(available_types['entity_categories'])}")
        
        # Show some available types
        print(f"\n📋 Available Entity Types (first 10):")
        for i, entity_type in enumerate(available_types['entity_types'][:10]):
            print(f"   {i+1}. {entity_type}")
        
        print(f"\n📋 Available Relationship Types (first 10):")
        for i, rel_type in enumerate(available_types['relationship_types'][:10]):
            print(f"   {i+1}. {rel_type}")
        
        search_engine.neo4j_client.close()
        return search_engine, available_types
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return None, None

def test_entity_neighborhood_search(sample_entities):
    """Test entity neighborhood search with real entities."""
    print("\n🌐 Testing Entity Neighborhood Search")
    print("=" * 50)
    
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        
        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )
        
        # Test with different entity types
        test_entities = []
        if sample_entities['persons']:
            test_entities.append(('Person', sample_entities['persons'][0]))
        if sample_entities['companies']:
            test_entities.append(('Company', sample_entities['companies'][0]))
        if sample_entities['projects']:
            test_entities.append(('Project', sample_entities['projects'][0]))
        
        if not test_entities:
            print("⚠️  No sample entities found for testing")
            return
        
        for entity_type, entity_name in test_entities:
            print(f"\n🔍 Testing neighborhood for {entity_type}: '{entity_name}'")
            
            result = search_engine.search_entity_neighborhood(
                entity_name=entity_name,
                max_depth=2,
                max_results=20
            )
            
            print(f"   ✅ Search completed in {result.processing_time_ms:.2f}ms")
            print(f"   📊 Results: {result.total_results} total")
            print(f"   🏷️  Entities found: {len(result.graph_context.entities)}")
            print(f"   🔗 Relationships found: {len(result.graph_context.relationships)}")
            print(f"   📈 Coverage score: {result.coverage_score:.3f}")
            print(f"   🎯 Coherence score: {result.coherence_score:.3f}")
            print(f"   ⭐ Relevance score: {result.relevance_score:.3f}")
            
            # Show connected entities
            if result.graph_context.entities:
                print(f"   🔗 Connected entities (first 5):")
                for i, entity in enumerate(result.graph_context.entities[:5]):
                    print(f"      {i+1}. {entity.name} ({entity.entity_type})")
            
            # Show relationships
            if result.graph_context.relationships:
                print(f"   🔗 Relationships (first 3):")
                for i, rel in enumerate(result.graph_context.relationships[:3]):
                    print(f"      {i+1}. {rel.source_entity} -[{rel.relationship_type}]-> {rel.target_entity}")
        
        search_engine.neo4j_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Entity neighborhood search failed: {e}")
        return False

def test_chunk_based_search(sample_chunks):
    """Test hybrid search with chunk indices."""
    print("\n📄 Testing Chunk-Based Hybrid Search")
    print("=" * 50)

    if not sample_chunks:
        print("⚠️  No sample chunks found for testing")
        return False

    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy

        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )

        # Test with different strategies
        strategies = [
            SearchStrategy.CHUNK_EXPANSION,
            SearchStrategy.ENTITY_CENTRIC,
            SearchStrategy.HYBRID
        ]

        test_chunks = sample_chunks[:3]  # Use first 3 chunks

        for strategy in strategies:
            print(f"\n🔍 Testing {strategy.value} strategy with chunks: {test_chunks}")

            try:
                result = search_engine.search(
                    chunk_indices=test_chunks,
                    query_text="test query for hybrid search",
                    strategy=strategy,
                    max_results=30,
                    expansion_depth=2
                )

                print(f"   ✅ Search completed in {result.processing_time_ms:.2f}ms")
                print(f"   📊 Total results: {result.total_results}")
                print(f"   🏷️  Entities: {len(result.graph_context.entities)}")
                print(f"   🔗 Relationships: {len(result.graph_context.relationships)}")
                print(f"   📈 Coverage score: {result.coverage_score:.3f}")
                print(f"   🎯 Coherence score: {result.coherence_score:.3f}")
                print(f"   ⭐ Relevance score: {result.relevance_score:.3f}")

                # Show debug info if there are errors
                if result.debug_info and 'error' in result.debug_info:
                    print(f"   ⚠️  Debug info: {result.debug_info}")

                # Show some results
                if result.graph_context.entities:
                    print(f"   🔗 Sample entities (first 3):")
                    for i, entity in enumerate(result.graph_context.entities[:3]):
                        print(f"      {i+1}. {entity.name} ({entity.entity_type})")

                if result.graph_context.relationships:
                    print(f"   🔗 Sample relationships (first 3):")
                    for i, rel in enumerate(result.graph_context.relationships[:3]):
                        print(f"      {i+1}. {rel.source_entity} -[{rel.relationship_type}]-> {rel.target_entity}")

            except Exception as strategy_error:
                print(f"   ❌ Strategy {strategy.value} failed: {strategy_error}")
                import traceback
                traceback.print_exc()

        search_engine.neo4j_client.close()
        return True

    except Exception as e:
        print(f"❌ Chunk-based search failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_graph_traversal_and_scoring():
    """Test graph traversal and scoring functionality specifically."""
    print("\n🎯 Testing Graph Traversal and Scoring")
    print("=" * 50)

    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy

        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )

        # First, let's check what chunks exist and pick some real ones
        print("🔍 Finding real chunks in the database...")

        query = "MATCH (c:Chunk) RETURN c.id LIMIT 10"
        result = search_engine.neo4j_client.execute_query(query)

        if not result:
            print("⚠️  No chunks found in database")
            search_engine.neo4j_client.close()
            return False

        real_chunks = [record['c.id'] for record in result]
        print(f"   Found {len(real_chunks)} chunks: {real_chunks[:3]}...")

        # Test with a small set of real chunks
        test_chunks = real_chunks[:2]

        print(f"\n🧪 Testing traversal with chunks: {test_chunks}")

        # Test chunk expansion strategy specifically
        result = search_engine.search(
            chunk_indices=test_chunks,
            query_text="test traversal and scoring",
            strategy=SearchStrategy.CHUNK_EXPANSION,
            max_results=20,
            expansion_depth=1,  # Start with depth 1
            include_chunk_context=True,
            include_file_context=True
        )

        print(f"✅ Traversal completed in {result.processing_time_ms:.2f}ms")
        print(f"📊 Results found:")
        print(f"   - Total results: {result.total_results}")
        print(f"   - Entities: {len(result.graph_context.entities)}")
        print(f"   - Relationships: {len(result.graph_context.relationships)}")
        print(f"   - Source chunks: {len(result.graph_context.source_chunks)}")

        print(f"📈 Scoring metrics:")
        print(f"   - Coverage score: {result.coverage_score:.3f}")
        print(f"   - Coherence score: {result.coherence_score:.3f}")
        print(f"   - Relevance score: {result.relevance_score:.3f}")

        # Show detailed results if found
        if result.graph_context.entities:
            print(f"\n🏷️  Entities found:")
            for i, entity in enumerate(result.graph_context.entities[:5]):
                print(f"   {i+1}. {entity.name} ({entity.entity_type}) - Score: {entity.relevance_score:.3f}")

        if result.graph_context.relationships:
            print(f"\n🔗 Relationships found:")
            for i, rel in enumerate(result.graph_context.relationships[:5]):
                print(f"   {i+1}. {rel.source_entity} -[{rel.relationship_type}]-> {rel.target_entity}")
                print(f"      Confidence: {rel.confidence_score:.3f}, Relevance: {rel.relevance_score:.3f}")

        # Test with deeper traversal
        if result.total_results > 0:
            print(f"\n🔄 Testing deeper traversal (depth=2)...")

            deeper_result = search_engine.search(
                chunk_indices=test_chunks,
                query_text="test deeper traversal",
                strategy=SearchStrategy.CHUNK_EXPANSION,
                max_results=30,
                expansion_depth=2,
                include_chunk_context=True
            )

            print(f"   Deeper traversal results: {deeper_result.total_results} (vs {result.total_results} at depth 1)")
            print(f"   Entities: {len(deeper_result.graph_context.entities)} (vs {len(result.graph_context.entities)})")
            print(f"   Relationships: {len(deeper_result.graph_context.relationships)} (vs {len(result.graph_context.relationships)})")

        search_engine.neo4j_client.close()
        return True

    except Exception as e:
        print(f"❌ Graph traversal test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_query_aware_entity_scoring():
    """Test the new query-aware entity scoring functionality."""
    print("\n🔍 Testing Query-Aware Entity Scoring")
    print("=" * 50)

    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy

        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )

        # Get some real chunks
        query = "MATCH (c:Chunk) RETURN c.id LIMIT 5"
        result = search_engine.neo4j_client.execute_query(query)
        real_chunks = [record['c.id'] for record in result][:2]

        # Test different query types to see how scoring changes
        test_queries = [
            "machine learning project development",
            "team collaboration and management",
            "artificial intelligence platform",
            "software engineering tools",
            "business process optimization"
        ]

        for query_text in test_queries:
            print(f"\n🔍 Testing query: '{query_text}'")

            result = search_engine.search(
                chunk_indices=real_chunks,
                query_text=query_text,
                strategy=SearchStrategy.CHUNK_EXPANSION,
                max_results=15,
                expansion_depth=1
            )

            print(f"   Results: {result.total_results} entities found")

            if result.graph_context.entities:
                # Show top 3 entities with their relevance scores
                top_entities = sorted(result.graph_context.entities,
                                    key=lambda e: e.relevance_score, reverse=True)[:3]

                print(f"   Top entities by query relevance:")
                for i, entity in enumerate(top_entities):
                    print(f"      {i+1}. {entity.name} ({entity.entity_type})")
                    print(f"         Relevance: {entity.relevance_score:.3f}")
                    print(f"         Match reason: {entity.match_reason}")

        # Compare query-aware vs non-query-aware scoring
        print(f"\n🔄 Comparing Query-Aware vs Traditional Scoring")

        test_query = "machine learning project"

        # Query-aware search
        query_aware_result = search_engine.search(
            chunk_indices=real_chunks,
            query_text=test_query,
            strategy=SearchStrategy.CHUNK_EXPANSION,
            max_results=10,
            expansion_depth=1
        )

        # Traditional search (without query text)
        traditional_result = search_engine.search(
            chunk_indices=real_chunks,
            query_text=None,  # No query text
            strategy=SearchStrategy.CHUNK_EXPANSION,
            max_results=10,
            expansion_depth=1
        )

        print(f"   Query-aware results: {len(query_aware_result.graph_context.entities)} entities")
        print(f"   Traditional results: {len(traditional_result.graph_context.entities)} entities")

        if query_aware_result.graph_context.entities and traditional_result.graph_context.entities:
            print(f"\n   Top entity comparison:")

            qa_top = sorted(query_aware_result.graph_context.entities,
                          key=lambda e: e.relevance_score, reverse=True)[0]
            trad_top = sorted(traditional_result.graph_context.entities,
                            key=lambda e: e.relevance_score, reverse=True)[0]

            print(f"   Query-aware top: {qa_top.name} (score: {qa_top.relevance_score:.3f})")
            print(f"   Traditional top: {trad_top.name} (score: {trad_top.relevance_score:.3f})")

        search_engine.neo4j_client.close()
        return True

    except Exception as e:
        print(f"❌ Query-aware scoring test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 Hybrid Search Engine Test Suite")
    print("=" * 60)
    
    # Step 1: Check Neo4j data
    neo4j_data = check_neo4j_data()
    if not neo4j_data:
        print("❌ Cannot proceed without Neo4j data")
        return
    
    # Step 2: Test initialization
    search_engine, available_types = test_hybrid_search_initialization()
    if not search_engine:
        print("❌ Cannot proceed without search engine")
        return
    
    # Step 3: Test entity neighborhood search
    if any(neo4j_data['sample_entities'].values()):
        test_entity_neighborhood_search(neo4j_data['sample_entities'])
    else:
        print("⚠️  Skipping entity neighborhood test - no sample entities found")
    
    # Step 4: Test chunk-based search
    if neo4j_data['sample_chunks']:
        test_chunk_based_search(neo4j_data['sample_chunks'])
    else:
        print("⚠️  Skipping chunk-based test - no sample chunks found")

    # Step 5: Test graph traversal and scoring specifically
    print("\n" + "=" * 60)
    test_graph_traversal_and_scoring()

    # Step 6: Test query-aware entity scoring
    print("\n" + "=" * 60)
    test_query_aware_entity_scoring()

    print("\n🎉 Test Suite Complete!")
    print("=" * 60)

if __name__ == "__main__":
    main()
