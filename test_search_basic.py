#!/usr/bin/env python3
"""
Basic test for the hybrid search engine components
"""

import sys
import traceback

def test_constants():
    """Test loading constants"""
    print("🔧 Testing Constants...")
    try:
        from enterprise_kg_minimal.constants.entities import get_all_entity_types, get_entity_properties
        from enterprise_kg_minimal.constants.relationships import get_all_relationship_types
        
        entity_types = get_all_entity_types()
        relationship_types = get_all_relationship_types()
        
        print(f"✅ Constants loaded successfully:")
        print(f"   Entity types: {len(entity_types)}")
        print(f"   Relationship types: {len(relationship_types)}")
        print(f"   Sample entity types: {list(entity_types)[:3]}")
        print(f"   Sample relationship types: {list(relationship_types)[:3]}")
        
        # Test entity properties
        if 'Person' in entity_types:
            person_props = get_entity_properties('Person')
            print(f"   Person properties: {list(person_props.keys())[:3]}")
        
        return True
    except Exception as e:
        print(f"❌ Constants test failed: {e}")
        traceback.print_exc()
        return False

def test_search_schemas():
    """Test search schemas"""
    print("\n📋 Testing Search Schemas...")
    try:
        from enterprise_kg_minimal.search.search_schemas import (
            SearchQuery, SearchResult, SearchStrategy, EntityMatch, RelationshipMatch
        )
        
        # Create a test query
        query = SearchQuery(
            chunk_indices=["test_chunk_1", "test_chunk_2"],
            query_text="test query",
            strategy=SearchStrategy.HYBRID
        )
        
        print(f"✅ Search schemas working:")
        print(f"   Query created with {len(query.chunk_indices)} chunks")
        print(f"   Strategy: {query.strategy.value}")
        
        return True
    except Exception as e:
        print(f"❌ Search schemas test failed: {e}")
        traceback.print_exc()
        return False

def test_graph_rag():
    """Test GraphRAG (without Neo4j connection)"""
    print("\n🧠 Testing GraphRAG...")
    try:
        from enterprise_kg_minimal.search.graph_rag import GraphRAG
        
        # We can't test with real Neo4j, but we can test class creation
        print("✅ GraphRAG class imported successfully")
        print("   (Neo4j connection test skipped - requires running database)")
        
        return True
    except Exception as e:
        print(f"❌ GraphRAG test failed: {e}")
        traceback.print_exc()
        return False

def test_search_strategies():
    """Test search strategies"""
    print("\n⚖️  Testing Search Strategies...")
    try:
        from enterprise_kg_minimal.search.search_strategies import (
            EntityCentricStrategy, RelationshipCentricStrategy, 
            ChunkExpansionStrategy, HierarchicalStrategy
        )
        
        print("✅ Search strategies imported successfully:")
        print("   - EntityCentricStrategy")
        print("   - RelationshipCentricStrategy") 
        print("   - ChunkExpansionStrategy")
        print("   - HierarchicalStrategy")
        
        return True
    except Exception as e:
        print(f"❌ Search strategies test failed: {e}")
        traceback.print_exc()
        return False

def test_result_aggregator():
    """Test result aggregator"""
    print("\n📊 Testing Result Aggregator...")
    try:
        from enterprise_kg_minimal.search.result_aggregator import SearchResultAggregator
        
        aggregator = SearchResultAggregator()
        print(f"✅ Result aggregator created successfully")
        print(f"   Entity dedup threshold: {aggregator.entity_deduplication_threshold}")
        print(f"   Relationship dedup threshold: {aggregator.relationship_deduplication_threshold}")
        
        return True
    except Exception as e:
        print(f"❌ Result aggregator test failed: {e}")
        traceback.print_exc()
        return False

def test_hybrid_search_engine():
    """Test hybrid search engine (without Neo4j)"""
    print("\n🚀 Testing Hybrid Search Engine...")
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import HybridSearchEngine
        
        print("✅ Hybrid search engine imported successfully")
        print("   (Full functionality test requires Neo4j connection)")
        
        return True
    except Exception as e:
        print(f"❌ Hybrid search engine test failed: {e}")
        traceback.print_exc()
        return False

def test_package_integration():
    """Test package-level integration"""
    print("\n📦 Testing Package Integration...")
    try:
        # Test if we can import from the main package
        from enterprise_kg_minimal.search import HybridSearchEngine
        
        print("✅ Package integration working")
        print("   Can import HybridSearchEngine from enterprise_kg_minimal.search")
        
        return True
    except Exception as e:
        print(f"❌ Package integration test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Enterprise KG Hybrid Search - Basic Tests")
    print("=" * 60)
    
    tests = [
        ("Constants", test_constants),
        ("Search Schemas", test_search_schemas),
        ("GraphRAG", test_graph_rag),
        ("Search Strategies", test_search_strategies),
        ("Result Aggregator", test_result_aggregator),
        ("Hybrid Search Engine", test_hybrid_search_engine),
        ("Package Integration", test_package_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 30)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed! The hybrid search engine components are working.")
        print("\n💡 Next steps:")
        print("   1. Start Neo4j database")
        print("   2. Run full integration tests")
        print("   3. Test with real data")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
