#!/usr/bin/env python3
"""
Test search functionality with mock data (no Neo4j required)
"""

import sys
import traceback
from unittest.mock import Mock, MagicMock

def test_search_query_creation():
    """Test creating search queries with different parameters"""
    print("🔍 Testing Search Query Creation...")
    try:
        from enterprise_kg_minimal.search.search_schemas import SearchQuery, SearchStrategy
        
        # Test basic query
        basic_query = SearchQuery(
            chunk_indices=["chunk1", "chunk2", "chunk3"],
            query_text="Who manages the AI project?",
            strategy=SearchStrategy.HYBRID
        )
        
        print(f"✅ Basic query created:")
        print(f"   Chunks: {len(basic_query.chunk_indices)}")
        print(f"   Strategy: {basic_query.strategy.value}")
        print(f"   Max results: {basic_query.max_results}")
        print(f"   Expansion depth: {basic_query.expansion_depth}")
        
        # Test query with filters
        filtered_query = SearchQuery(
            chunk_indices=["chunk1"],
            entity_types={"Person", "Manager", "Project"},
            relationship_types={"manages", "works_for"},
            max_results=20,
            expansion_depth=3
        )
        
        print(f"✅ Filtered query created:")
        print(f"   Entity types: {filtered_query.entity_types}")
        print(f"   Relationship types: {filtered_query.relationship_types}")
        
        return True
    except Exception as e:
        print(f"❌ Search query test failed: {e}")
        traceback.print_exc()
        return False

def test_entity_and_relationship_matches():
    """Test creating entity and relationship matches"""
    print("\n👥 Testing Entity and Relationship Matches...")
    try:
        from enterprise_kg_minimal.search.search_schemas import EntityMatch, RelationshipMatch
        from datetime import datetime
        
        # Test entity match
        entity = EntityMatch(
            name="John Smith",
            entity_type="Manager",
            node_id="123",
            properties={"department": "Engineering", "level": "Senior"},
            relevance_score=0.95,
            match_reason="extracted_from_chunk",
            chunk_sources=["chunk1", "chunk2"],
            created_at=datetime.now()
        )
        
        print(f"✅ Entity match created:")
        print(f"   Name: {entity.name}")
        print(f"   Type: {entity.entity_type}")
        print(f"   Relevance: {entity.relevance_score}")
        print(f"   Chunk sources: {len(entity.chunk_sources)}")
        
        # Test relationship match
        relationship = RelationshipMatch(
            source_entity="John Smith",
            target_entity="AI Project",
            relationship_type="manages",
            confidence_score=0.87,
            relevance_score=0.92,
            context="John Smith manages the AI Project team",
            match_reason="entity_connection"
        )
        
        print(f"✅ Relationship match created:")
        print(f"   {relationship.source_entity} --[{relationship.relationship_type}]--> {relationship.target_entity}")
        print(f"   Confidence: {relationship.confidence_score}")
        print(f"   Relevance: {relationship.relevance_score}")
        
        return True
    except Exception as e:
        print(f"❌ Entity/Relationship match test failed: {e}")
        traceback.print_exc()
        return False

def test_search_result_structure():
    """Test search result structure and methods"""
    print("\n📊 Testing Search Result Structure...")
    try:
        from enterprise_kg_minimal.search.search_schemas import (
            SearchResult, SearchQuery, GraphContext, EntityMatch, RelationshipMatch, SearchStrategy
        )
        from datetime import datetime
        
        # Create mock entities and relationships
        entities = [
            EntityMatch(
                name="John Smith",
                entity_type="Manager",
                node_id="1",
                relevance_score=0.95,
                match_reason="high_importance"
            ),
            EntityMatch(
                name="AI Project",
                entity_type="Project",
                node_id="2",
                relevance_score=0.88,
                match_reason="extracted_from_chunk"
            ),
            EntityMatch(
                name="Python",
                entity_type="Technology",
                node_id="3",
                relevance_score=0.75,
                match_reason="technology_mention"
            )
        ]
        
        relationships = [
            RelationshipMatch(
                source_entity="John Smith",
                target_entity="AI Project",
                relationship_type="manages",
                confidence_score=0.92,
                relevance_score=0.90
            ),
            RelationshipMatch(
                source_entity="AI Project",
                target_entity="Python",
                relationship_type="uses",
                confidence_score=0.85,
                relevance_score=0.80
            )
        ]
        
        # Create graph context
        graph_context = GraphContext(
            entities=entities,
            relationships=relationships,
            total_entities=len(entities),
            total_relationships=len(relationships),
            entity_types_found={"Manager", "Project", "Technology"},
            relationship_types_found={"manages", "uses"}
        )
        
        # Create search query
        query = SearchQuery(
            chunk_indices=["chunk1", "chunk2"],
            strategy=SearchStrategy.HYBRID
        )
        
        # Create search result
        result = SearchResult(
            query=query,
            graph_context=graph_context,
            total_results=len(entities) + len(relationships),
            processing_time_ms=150.5,
            strategy_used=SearchStrategy.HYBRID,
            coverage_score=0.85,
            coherence_score=0.92,
            relevance_score=0.78
        )
        
        print(f"✅ Search result created:")
        print(f"   Total results: {result.total_results}")
        print(f"   Processing time: {result.processing_time_ms}ms")
        print(f"   Strategy: {result.strategy_used.value}")
        print(f"   Quality scores: Coverage={result.coverage_score:.2f}, Coherence={result.coherence_score:.2f}, Relevance={result.relevance_score:.2f}")
        
        # Test result methods
        top_entities = result.get_top_entities(2)
        print(f"   Top 2 entities: {[e.name for e in top_entities]}")
        
        top_relationships = result.get_top_relationships(2)
        print(f"   Top 2 relationships: {[f'{r.source_entity}->{r.target_entity}' for r in top_relationships]}")
        
        managers = result.get_entity_by_type("Manager")
        print(f"   Managers found: {[e.name for e in managers]}")
        
        return True
    except Exception as e:
        print(f"❌ Search result test failed: {e}")
        traceback.print_exc()
        return False

def test_search_parameter_validation():
    """Test search parameter validation"""
    print("\n✅ Testing Search Parameter Validation...")
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import HybridSearchEngine
        from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient
        
        # Create a mock Neo4j client
        mock_neo4j_client = Mock(spec=Neo4jClient)
        
        # Create search engine with mock client
        search_engine = HybridSearchEngine(mock_neo4j_client)
        
        # Test parameter validation
        validation = search_engine.validate_search_parameters(
            chunk_indices=["chunk1", "chunk2"],
            entity_types={"Person", "Manager"},  # Valid types
            relationship_types={"manages", "works_for"},  # Valid types
            expansion_depth=2,
            max_results=50
        )
        
        print(f"✅ Valid parameters validation:")
        print(f"   Valid: {validation['valid']}")
        print(f"   Warnings: {len(validation['warnings'])}")
        print(f"   Errors: {len(validation['errors'])}")
        
        # Test with invalid parameters
        invalid_validation = search_engine.validate_search_parameters(
            chunk_indices=[],  # Empty - should be invalid
            entity_types={"InvalidType"},  # Invalid type
            relationship_types={"invalid_rel"},  # Invalid type
            expansion_depth=0,  # Invalid depth
            max_results=0  # Invalid max results
        )
        
        print(f"✅ Invalid parameters validation:")
        print(f"   Valid: {invalid_validation['valid']}")
        print(f"   Warnings: {len(invalid_validation['warnings'])}")
        print(f"   Errors: {len(invalid_validation['errors'])}")
        
        # Test available types
        available_types = search_engine.get_available_types()
        print(f"✅ Available types retrieved:")
        print(f"   Entity types: {available_types['total_entity_types']}")
        print(f"   Relationship types: {available_types['total_relationship_types']}")
        
        return True
    except Exception as e:
        print(f"❌ Parameter validation test failed: {e}")
        traceback.print_exc()
        return False

def test_result_aggregation():
    """Test result aggregation functionality"""
    print("\n🔄 Testing Result Aggregation...")
    try:
        from enterprise_kg_minimal.search.result_aggregator import SearchResultAggregator
        from enterprise_kg_minimal.search.search_schemas import (
            SearchResult, SearchQuery, GraphContext, EntityMatch, SearchStrategy
        )
        
        aggregator = SearchResultAggregator()
        
        # Create mock search results
        entities1 = [
            EntityMatch(name="John Smith", entity_type="Manager", node_id="1", relevance_score=0.9),
            EntityMatch(name="AI Project", entity_type="Project", node_id="2", relevance_score=0.8)
        ]
        
        entities2 = [
            EntityMatch(name="John Smith", entity_type="Manager", node_id="1", relevance_score=0.85),  # Duplicate
            EntityMatch(name="Python", entity_type="Technology", node_id="3", relevance_score=0.7)
        ]
        
        result1 = SearchResult(
            query=SearchQuery(chunk_indices=["chunk1"]),
            graph_context=GraphContext(entities=entities1),
            total_results=2,
            strategy_used=SearchStrategy.ENTITY_CENTRIC,
            coverage_score=0.8,
            coherence_score=0.9,
            relevance_score=0.85
        )
        
        result2 = SearchResult(
            query=SearchQuery(chunk_indices=["chunk2"]),
            graph_context=GraphContext(entities=entities2),
            total_results=2,
            strategy_used=SearchStrategy.CHUNK_EXPANSION,
            coverage_score=0.7,
            coherence_score=0.8,
            relevance_score=0.75
        )
        
        # Test aggregation
        query = SearchQuery(chunk_indices=["chunk1", "chunk2"])
        aggregated = aggregator.aggregate_results([result1, result2], query)
        
        print(f"✅ Result aggregation completed:")
        print(f"   Original results: 2")
        print(f"   Aggregated entities: {len(aggregated.graph_context.entities)}")
        print(f"   Strategy used: {aggregated.strategy_used.value}")
        print(f"   Coverage score: {aggregated.coverage_score:.2f}")
        print(f"   Coherence score: {aggregated.coherence_score:.2f}")
        print(f"   Relevance score: {aggregated.relevance_score:.2f}")
        
        # Check deduplication worked
        entity_names = [e.name for e in aggregated.graph_context.entities]
        print(f"   Entity names: {entity_names}")
        print(f"   Deduplication working: {'John Smith' in entity_names and entity_names.count('John Smith') == 1}")
        
        return True
    except Exception as e:
        print(f"❌ Result aggregation test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all functionality tests"""
    print("🚀 Enterprise KG Hybrid Search - Functionality Tests")
    print("=" * 60)
    print("Testing search functionality with mock data (no Neo4j required)")
    print()
    
    tests = [
        ("Search Query Creation", test_search_query_creation),
        ("Entity and Relationship Matches", test_entity_and_relationship_matches),
        ("Search Result Structure", test_search_result_structure),
        ("Search Parameter Validation", test_search_parameter_validation),
        ("Result Aggregation", test_result_aggregation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Functionality Test Summary")
    print("=" * 40)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} functionality tests passed")
    
    if passed == total:
        print("🎉 All functionality tests passed!")
        print("\n✨ The hybrid search engine is working correctly!")
        print("\n🔧 What was tested:")
        print("   ✅ Search query creation and configuration")
        print("   ✅ Entity and relationship matching")
        print("   ✅ Search result structure and methods")
        print("   ✅ Parameter validation and type checking")
        print("   ✅ Result aggregation and deduplication")
        print("\n🚀 Ready for integration with:")
        print("   • Neo4j knowledge graph database")
        print("   • Pinecone vector similarity search")
        print("   • Your enterprise document processing pipeline")
    else:
        print("⚠️  Some functionality tests failed. Check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
