# Hybrid Search Relevance Scoring & Ranking Algorithm

## Overview
The hybrid search engine combines vector similarity search results (chunk indices) with graph-based retrieval to provide contextually rich search results. The core innovation lies in how we calculate relevance scores for entities and relationships during graph traversal.

## Core Algorithm Components

### 1. Initial Entity Scoring
When entities are first extracted from chunks, they receive a base relevance score:

```python
# Base score from entity properties (importance in knowledge graph)
base_score = entity_properties.get("graph_importance", 0.5)  # 0.0-1.0

# Entities directly from chunks get high initial relevance
initial_relevance = base_score + 0.3  # Boost for direct chunk extraction
```

### 2. Hybrid Neighbor Scoring Formula
For each neighboring entity discovered during graph traversal, we calculate a composite relevance score using multiple weighted components:

```python
relevance_score = base_score + 
                 confidence_component + 
                 connectivity_component + 
                 cooccurrence_component + 
                 priority_inheritance - 
                 depth_penalty
```

#### Component Breakdown:

**A. Confidence Component (40% weight)**
- Uses relationship confidence scores from the knowledge graph
- Formula: `confidence_score * 0.4`
- Range: 0.0 - 0.4

**B. Connectivity Component (30% weight)**  
- Measures how well-connected an entity is in the graph
- Formula: `min(connection_count / 10.0, 1.0) * 0.3`
- Entities with more relationships get higher scores

**C. Co-occurrence Component (20% weight)**
- Entities appearing in the same chunks as query entities get boosted
- Formula: `min(shared_chunks / 5.0, 1.0) * 0.2`
- Promotes semantic similarity through document co-occurrence

**D. Priority Inheritance (10% weight)**
- Entities connected to high-priority entities inherit some priority
- Formula: `parent_priority * 0.1`
- Ensures important entity neighborhoods are well-represented

**E. Depth Penalty**
- Reduces score for entities found deeper in traversal
- Formula: `0.05 * depth`
- Prevents over-expansion and maintains relevance focus

### 3. Relationship Scoring
Relationships between entities are scored based on:

```python
relationship_relevance = (confidence_score * 0.8) + 
                        (context_quality * 0.2)

# Where context_quality considers:
# - Length and richness of relationship context
# - Source sentence quality
# - Multiple mentions across chunks
```

### 4. Priority Queue Traversal
Instead of traditional breadth-first search, we use a priority queue:

```python
# Entities are processed in order of relevance, not discovery order
priority_queue = [(-relevance_score, depth, entity_name, entity_data)]

# This ensures high-value paths are explored first
# and low-value branches are pruned early
```

### 5. Result Aggregation & Ranking

#### Entity Ranking Factors:
1. **Base Relevance Score** (primary)
2. **Entity Type Matching** (1.2x boost if matches query filters)
3. **High Importance Boost** (1.1x boost for importance > 0.7)
4. **Multi-chunk Presence** (+0.05 per additional chunk source)

#### Relationship Ranking Factors:
1. **Relevance Score** (primary)
2. **Confidence Score** (secondary)
3. **Relationship Type Matching** (1.2x boost if matches query filters)
4. **Context Richness** (+0.1 for substantial context)

### 6. Quality Metrics Calculation

**Coverage Score:**
```
coverage = (unique_entity_types_found / total_available_entity_types) + 
           diversity_boost
```

**Coherence Score:**
```
coherence = relationships_count / max(1, entities_count)
# Measures how well-connected the result graph is
```

**Relevance Score:**
```
relevance = weighted_average_of_individual_relevance_scores
# Weighted by result count for each strategy
```

## Key Algorithmic Advantages

### 1. **Multi-Signal Fusion**
- Combines graph structure, semantic similarity, and relationship strength
- No single factor dominates the scoring

### 2. **Adaptive Traversal**
- High-value paths explored first via priority queue
- Early termination for low-value branches
- Configurable depth and queue limits

### 3. **Context Preservation**
- Chunk co-occurrence maintains semantic relationships
- Priority inheritance preserves important entity neighborhoods

### 4. **Scalable Performance**
- Priority queue limits prevent exponential expansion
- Depth penalties focus on most relevant results
- Early termination based on queue size

## Configuration Parameters

The algorithm is highly configurable through weights:

- `confidence_weight: 0.4` - Relationship confidence importance
- `connectivity_weight: 0.3` - Graph connectivity importance  
- `cooccurrence_weight: 0.2` - Chunk co-occurrence importance
- `priority_inheritance_weight: 0.1` - Parent priority inheritance
- `expansion_depth: 2` - Maximum traversal depth
- `priority_queue_limit: 1000` - Performance safety limit

## Result Quality Assurance

### Deduplication Strategy:
- Entities deduplicated by `(name, type)` pairs
- Relationships deduplicated by `(source, target, type)` triplets
- Multiple mentions boost relevance scores

### Score Normalization:
- All component scores normalized to 0.0-1.0 range
- Final scores clamped to prevent overflow
- Consistent scoring across different graph sizes

This hybrid approach ensures that search results are both semantically relevant (through chunk co-occurrence) and structurally meaningful (through graph connectivity), while maintaining computational efficiency through intelligent traversal strategies.
